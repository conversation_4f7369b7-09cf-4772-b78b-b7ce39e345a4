import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";
import {
  AppSidebar,
  SidebarWrapper,
  SidebarInset,
} from "@/components/dashboard/layout/app-sidebar";
import { AuthProvider } from "@propelauth/nextjs/client";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Social Media Admin Dashboard",
  description: "Manage your social media analytics and connections",
  generator: "v0.dev",
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider authUrl={process.env.NEXT_PUBLIC_AUTH_URL!}>
      {/*}<SidebarProvider>{*/}
      <SidebarWrapper>
        <AppSidebar />
        <SidebarInset>{children}</SidebarInset>
        {/*}</SidebarProvider>{*/}
      </SidebarWrapper>
      <Toaster />
    </AuthProvider>
  );
}
