"use client";

import type React from "react";
import { useEffect, useState, useMemo } from "react";
import { useParams } from "next/navigation";
import {
  LayoutDashboard,
  Newspaper,
  ImageIcon,
  LinkIcon,
  Video,
  ListChecks,
  BarChart3,
} from "lucide-react";
import { useProjectPage } from "@/lib/contexts/project-page-context";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SocialMediaPostCard } from "@/components/social-media-post-card";
import type {
  Feed,
  SocialMediaPost,
  SocialMediaAccount,
  MediaItem,
} from "@/lib/types";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// --- MOCK DATA ---
// (Assuming MOCK_ACCOUNTS, MOCK_FEEDS, MOCK_POSTS are defined as in previous examples,
// ensure MOCK_POSTS has diverse content: text-only, images, videos, links)

const MOCK_ACCOUNTS: SocialMediaAccount[] = [
  {
    id: "acc_tw_001",
    name: "Vercel",
    username: "vercel",
    platform: "Twitter",
    avatarUrl: "/placeholder.svg?width=40&height=40&text=V",
    isVerified: true,
  },
  {
    id: "acc_tw_002",
    name: "Next.js",
    username: "nextjs",
    platform: "Twitter",
    avatarUrl: "/placeholder.svg?width=40&height=40&text=N",
    isVerified: true,
  },
  {
    id: "acc_fb_001",
    name: "Meta Engineering",
    username: "metaengineering",
    platform: "Facebook",
    avatarUrl: "/placeholder.svg?width=40&height=40&text=M",
    isVerified: true,
  },
  {
    id: "acc_ig_001",
    name: "Instagram Creators",
    username: "igcreators",
    platform: "Instagram",
    avatarUrl: "/placeholder.svg?width=40&height=40&text=IG",
    isVerified: true,
  },
];

const MOCK_FEEDS: Feed[] = [
  {
    id: "feed_tw_001",
    name: "Vercel & Next.js Tweets",
    platform: "Twitter",
    description: "Tweets from Vercel & Next.js",
    status: "active",
    connectionAccountIds: ["acc_tw_001", "acc_tw_002"],
    createdAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "feed_fb_001",
    name: "Meta Engineering Updates",
    platform: "Facebook",
    description: "Posts from Meta Engineering",
    status: "active",
    connectionAccountIds: ["acc_fb_001"],
    createdAt: "2024-02-01T14:30:00Z",
  },
  {
    id: "feed_ig_001",
    name: "Featured Instagram Content",
    platform: "Instagram",
    description: "Curated posts from Instagram creators",
    status: "active",
    connectionAccountIds: ["acc_ig_001"],
    createdAt: "2024-03-01T11:00:00Z",
  },
];

const MOCK_POSTS: SocialMediaPost[] = [
  // Twitter Post with Image
  {
    id: "post_1",
    platform: "Twitter",
    author: MOCK_ACCOUNTS[0],
    content: "Vercel Edge Functions update! 🚀",
    timestamp: "2024-06-22T10:00:00Z",
    media: [
      {
        type: "image",
        url: "/placeholder.svg?width=600&height=337",
        altText: "Vercel Edge Update",
      },
    ],
    stats: { comments: 152, retweets: 890, likes: 2500 },
    postUrl: "#",
  },
  // Twitter Post with Link Preview
  {
    id: "post_2",
    platform: "Twitter",
    author: MOCK_ACCOUNTS[1],
    content: "Next.js 14.3 is here! #NextJS",
    timestamp: "2024-06-21T14:30:00Z",
    linkPreview: {
      url: "#",
      title: "Next.js 14.3 Release",
      description: "Latest features in Next.js 14.3.",
      imageUrl: "/placeholder.svg?width=500&height=250",
      domain: "nextjs.org",
    },
    stats: { comments: 210, retweets: 1200, likes: 3100 },
    postUrl: "#",
  },
  // Facebook Post with Multiple Images
  {
    id: "post_3",
    platform: "Facebook",
    author: MOCK_ACCOUNTS[2],
    content: "AI-driven content moderation research.",
    timestamp: "2024-06-20T09:15:00Z",
    media: [
      {
        type: "image",
        url: "/placeholder.svg?width=600&height=400",
        altText: "AI Research",
      },
      {
        type: "image",
        url: "/placeholder.svg?width=600&height=400",
        altText: "Data Viz",
      },
    ],
    stats: { comments: 78, retweets: 150, likes: 950 },
    postUrl: "#",
  },
  // Instagram Post with Video
  {
    id: "post_4",
    platform: "Instagram",
    author: MOCK_ACCOUNTS[3],
    content: "Amazing drone footage! 🚁 #drone #travel",
    timestamp: "2024-06-23T12:00:00Z",
    media: [
      {
        type: "video",
        url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
        thumbnailUrl: "/placeholder.svg?width=600&height=600",
        altText: "Drone footage",
      },
    ],
    stats: { comments: 300, likes: 5000, views: 150000 },
    postUrl: "#",
  },
  // Text-only Twitter Post
  {
    id: "post_5",
    platform: "Twitter",
    author: MOCK_ACCOUNTS[0],
    content:
      "Just a quick thought on serverless architecture and its benefits for scaling applications. What are your experiences?",
    timestamp: "2024-06-23T15:00:00Z",
    stats: { comments: 50, retweets: 120, likes: 450 },
    postUrl: "#",
  },
  // Instagram Post - Image
  {
    id: "post_6",
    platform: "Instagram",
    author: MOCK_ACCOUNTS[3],
    content: "Beautiful sunset from the coast. #sunset #nature",
    timestamp: "2024-06-24T18:30:00Z",
    media: [
      {
        type: "image",
        url: "/placeholder.svg?width=600&height=600",
        altText: "Sunset",
      },
    ],
    stats: { comments: 120, likes: 2300 },
    postUrl: "#",
  },
];
// --- END MOCK DATA ---

const getPostsForFeed = (feedId: string | null): SocialMediaPost[] => {
  if (!feedId) return [];
  const feed = MOCK_FEEDS.find((f) => f.id === feedId);
  if (!feed) return [];
  return MOCK_POSTS.filter(
    (post) =>
      feed.connectionAccountIds.includes(post.author.id) &&
      post.platform === feed.platform
  ).sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
};

interface DisplayAreaConfig {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  component: React.FC<{
    posts: SocialMediaPost[];
    selectedFeed: Feed | undefined;
  }>;
}

// --- Individual Component Renderers ---
const FullPostStream: React.FC<{ posts: SocialMediaPost[] }> = ({ posts }) => (
  <div className="space-y-4">
    {posts.length > 0 ? (
      posts.map((post) => <SocialMediaPostCard key={post.id} post={post} />)
    ) : (
      <p className="text-sm text-muted-foreground text-center py-8">
        No posts to display.
      </p>
    )}
  </div>
);

const ImageGallery: React.FC<{ posts: SocialMediaPost[] }> = ({ posts }) => {
  const imagePosts = posts.filter((post) =>
    post.media?.some((m) => m.type === "image")
  );
  const images: MediaItem[] = imagePosts.flatMap(
    (post) => post.media?.filter((m) => m.type === "image") || []
  );

  if (images.length === 0) {
    return (
      <p className="text-sm text-muted-foreground text-center py-8">
        No images found in this feed.
      </p>
    );
  }
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
      {images.map((image, index) => (
        <div
          key={index}
          className="aspect-square bg-muted rounded overflow-hidden"
        >
          <img
            src={image.url || "/placeholder.svg"}
            alt={image.altText || `Feed image ${index + 1}`}
            className="w-full h-full object-cover"
          />
        </div>
      ))}
    </div>
  );
};

const VideoShowcase: React.FC<{ posts: SocialMediaPost[] }> = ({ posts }) => {
  const videoPosts = posts.filter((post) =>
    post.media?.some((m) => m.type === "video")
  );
  if (videoPosts.length === 0) {
    return (
      <p className="text-sm text-muted-foreground text-center py-8">
        No videos found in this feed.
      </p>
    );
  }
  return (
    <div className="space-y-4">
      {videoPosts.map((post) =>
        post.media
          ?.filter((m) => m.type === "video")
          .map((video, index) => (
            <div
              key={`${post.id}-video-${index}`}
              className="bg-muted rounded overflow-hidden"
            >
              <video
                controls
                poster={video.thumbnailUrl || ""}
                className="w-full aspect-video"
              >
                <source src={video.url} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
              {post.content && (
                <p className="p-2 text-sm bg-background/50">
                  {post.content.substring(0, 100)}
                  {post.content.length > 100 ? "..." : ""}
                </p>
              )}
            </div>
          ))
      )}
    </div>
  );
};

const LinkPreviewList: React.FC<{ posts: SocialMediaPost[] }> = ({ posts }) => {
  const linkPosts = posts.filter((post) => post.linkPreview);
  if (linkPosts.length === 0) {
    return (
      <p className="text-sm text-muted-foreground text-center py-8">
        No posts with link previews found in this feed.
      </p>
    );
  }
  return (
    <div className="space-y-3">
      {linkPosts.map((post) => (
        <a
          key={post.id}
          href={post.linkPreview!.url}
          target="_blank"
          rel="noopener noreferrer"
          className="block p-3 border rounded hover:bg-muted transition-colors"
        >
          <h4 className="font-semibold">
            {post.linkPreview!.title || "Untitled Link"}
          </h4>
          <p className="text-sm text-muted-foreground truncate">
            {post.linkPreview!.description || post.content}
          </p>
          {post.linkPreview!.imageUrl && (
            <img
              src={post.linkPreview!.imageUrl || "/placeholder.svg"}
              alt={post.linkPreview!.title || "Link preview"}
              className="mt-2 rounded max-h-32 object-contain"
            />
          )}
          <p className="text-xs text-muted-foreground mt-1">
            {post.linkPreview!.domain ||
              new URL(post.linkPreview!.url).hostname}
          </p>
        </a>
      ))}
    </div>
  );
};

const CompactTextList: React.FC<{ posts: SocialMediaPost[] }> = ({ posts }) => {
  if (posts.length === 0) {
    return (
      <p className="text-sm text-muted-foreground text-center py-8">
        No posts to display.
      </p>
    );
  }
  return (
    <ul className="space-y-2">
      {posts.map((post) => (
        <li key={post.id} className="p-2 border rounded text-sm">
          <strong className="text-primary">{post.author.name}</strong> (
          {`@${post.author.username}`})
          <p className="mt-1">
            {post.content.substring(0, 150)}
            {post.content.length > 150 ? "..." : ""}
          </p>
        </li>
      ))}
    </ul>
  );
};

const StatsOverview: React.FC<{
  posts: SocialMediaPost[];
  selectedFeed: Feed | undefined;
}> = ({ posts, selectedFeed }) => {
  const totalPosts = posts.length;
  const totalLikes = posts.reduce(
    (sum, post) => sum + (post.stats.likes || 0),
    0
  );
  const totalComments = posts.reduce(
    (sum, post) => sum + (post.stats.comments || 0),
    0
  );
  const totalRetweets = posts.reduce(
    (sum, post) => sum + (post.stats.retweets || 0),
    0
  );
  const totalViews = posts.reduce(
    (sum, post) => sum + (post.stats.views || 0),
    0
  );

  if (!selectedFeed) {
    return (
      <p className="text-sm text-muted-foreground text-center py-8">
        Select a feed to see stats.
      </p>
    );
  }
  if (totalPosts === 0) {
    return (
      <p className="text-sm text-muted-foreground text-center py-8">
        No posts in this feed to calculate stats.
      </p>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalPosts}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Likes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {totalLikes.toLocaleString()}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Comments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {totalComments.toLocaleString()}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Total Shares/Retweets
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {totalRetweets.toLocaleString()}
          </div>
        </CardContent>
      </Card>
      {selectedFeed.platform !== "Facebook" &&
        posts.some((p) => p.stats.views !== undefined) && ( // Views are common on Twitter/Instagram
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {totalViews.toLocaleString()}
              </div>
            </CardContent>
          </Card>
        )}
    </div>
  );
};

// --- End Individual Component Renderers ---

const PREDEFINED_DISPLAY_AREAS: DisplayAreaConfig[] = [
  {
    id: "full_post_stream",
    title: "Full Post Stream",
    icon: <Newspaper className="h-5 w-5 mr-2" />,
    description: "Standard display of posts with all details.",
    component: FullPostStream,
  },
  {
    id: "image_gallery",
    title: "Image Gallery",
    icon: <ImageIcon className="h-5 w-5 mr-2" />,
    description: "Grid display of images from posts in the selected feed.",
    component: ImageGallery,
  },
  {
    id: "video_showcase",
    title: "Video Showcase",
    icon: <Video className="h-5 w-5 mr-2" />,
    description: "Highlights video content from the selected feed.",
    component: VideoShowcase,
  },
  {
    id: "link_preview_list",
    title: "Link Preview List",
    icon: <LinkIcon className="h-5 w-5 mr-2" />,
    description: "Lists posts that contain link previews.",
    component: LinkPreviewList,
  },
  {
    id: "compact_text_list",
    title: "Compact Text List",
    icon: <ListChecks className="h-5 w-5 mr-2" />,
    description: "A dense, text-focused list of posts.",
    component: CompactTextList,
  },
  {
    id: "stats_overview",
    title: "Feed Stats Overview",
    icon: <BarChart3 className="h-5 w-5 mr-2" />,
    description: "Summary of engagement statistics for the selected feed.",
    component: StatsOverview,
  },
];

export default function ProjectComponentsPage() {
  const params = useParams();
  const projectId = params.id as string;
  const { setPageTitle, setPageIcon, setHeaderActions, setShowBackButton } =
    useProjectPage();

  const [selectedGlobalFeedId, setSelectedGlobalFeedId] = useState<
    string | null
  >(null);
  const [availableFeeds, setAvailableFeeds] = useState<Feed[]>([]);

  useEffect(() => {
    setPageTitle("Components Showcase");
    setPageIcon(<LayoutDashboard className="h-5 w-5" />);
    setHeaderActions(null);
    setShowBackButton(false);
    setAvailableFeeds(MOCK_FEEDS); // Fetch/filter feeds for this project
  }, [
    setPageTitle,
    setPageIcon,
    setHeaderActions,
    setShowBackButton,
    projectId,
  ]);

  const postsForSelectedFeed = useMemo(() => {
    return getPostsForFeed(selectedGlobalFeedId);
  }, [selectedGlobalFeedId]);

  const selectedFeedDetails = useMemo(() => {
    return MOCK_FEEDS.find((feed) => feed.id === selectedGlobalFeedId);
  }, [selectedGlobalFeedId]);

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Global Feed Selector</CardTitle>
          <CardDescription>
            Select a feed below. All components on this page will display data
            from the chosen feed.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedGlobalFeedId || ""}
            onValueChange={(value) => setSelectedGlobalFeedId(value)}
          >
            <SelectTrigger className="w-full md:w-1/2 lg:w-1/3">
              <SelectValue placeholder="Select a feed for all components" />
            </SelectTrigger>
            <SelectContent>
              {availableFeeds.length > 0 ? (
                availableFeeds.map((feed) => (
                  <SelectItem key={feed.id} value={feed.id}>
                    {feed.name} ({feed.platform})
                  </SelectItem>
                ))
              ) : (
                <div className="p-4 text-sm text-muted-foreground text-center">
                  No feeds available.
                </div>
              )}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {!selectedGlobalFeedId && (
        <Alert>
          <LayoutDashboard className="h-4 w-4" />
          <AlertTitle>No Feed Selected</AlertTitle>
          <AlertDescription>
            Please select a feed from the dropdown above to view component
            examples.
          </AlertDescription>
        </Alert>
      )}

      {selectedGlobalFeedId && (
        <div className="space-y-8">
          {" "}
          {/* Vertical stacking for components */}
          {PREDEFINED_DISPLAY_AREAS.map((area) => (
            <Card key={area.id} className="flex flex-col">
              <CardHeader>
                <div className="flex items-center">
                  {area.icon}
                  <CardTitle className="text-lg">{area.title}</CardTitle>
                </div>
                <CardDescription>{area.description}</CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto max-h-[700px] p-4 pretty-scrollbar">
                <area.component
                  posts={postsForSelectedFeed}
                  selectedFeed={selectedFeedDetails}
                />
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
