"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { ArrowLeft, Bell, Facebook, Mail, Plus, Save, X } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/hooks/use-toast"
import Link from "next/link"

// Mock data
const connectionData = {
  id: "conn_001",
  name: "Company Facebook Page",
  platform: "Facebook",
  icon: Facebook,
}

const notificationEmails = ["<EMAIL>", "<EMAIL>"]

const notificationSettings = {
  connectionErrors: true,
  syncFailures: true,
  dataUpdates: false,
  accountChanges: true,
  permissionChanges: true,
  rateLimitWarnings: true,
}

export default function ConnectionNotificationsPage() {
  const params = useParams()
  const projectId = params.id as string
  const connectionId = params.connectionId as string

  const [emails, setEmails] = useState(notificationEmails)
  const [newEmail, setNewEmail] = useState("")
  const [isAddEmailOpen, setIsAddEmailOpen] = useState(false)
  const [notifications, setNotifications] = useState(notificationSettings)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const handleNotificationChange = (key: keyof typeof notificationSettings, value: boolean) => {
    setNotifications((prev) => ({ ...prev, [key]: value }))
    setHasUnsavedChanges(true)
  }

  const handleAddEmail = () => {
    if (newEmail && !emails.includes(newEmail)) {
      setEmails((prev) => [...prev, newEmail])
      setNewEmail("")
      setIsAddEmailOpen(false)
      setHasUnsavedChanges(true)
      toast({
        title: "Email added",
        description: "Notification email has been added successfully.",
      })
    }
  }

  const handleRemoveEmail = (emailToRemove: string) => {
    setEmails((prev) => prev.filter((email) => email !== emailToRemove))
    setHasUnsavedChanges(true)
    toast({
      title: "Email removed",
      description: "Notification email has been removed.",
    })
  }

  const handleSaveSettings = () => {
    setHasUnsavedChanges(false)
    toast({
      title: "Settings saved",
      description: "Connection notification settings have been updated successfully.",
    })
  }

  const IconComponent = connectionData.icon

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="flex h-16 items-center px-6">
          <div className="flex items-center space-x-4">
            <Link href={`/project/${projectId}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Project
              </Button>
            </Link>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-3">
              <IconComponent className="h-5 w-5" />
              <h1 className="text-xl font-semibold">{connectionData.name} - Notifications</h1>
              <Badge variant="outline">{connectionData.platform}</Badge>
            </div>
          </div>
          <div className="ml-auto">
            <Button onClick={handleSaveSettings} disabled={!hasUnsavedChanges} className="mr-2">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            {hasUnsavedChanges && <Badge variant="secondary">Unsaved changes</Badge>}
          </div>
        </div>
      </div>

      <div className="p-6 max-w-4xl mx-auto space-y-6">
        {/* Notification Recipients */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Mail className="h-5 w-5" />
                  <span>Notification Recipients</span>
                </CardTitle>
                <CardDescription>Manage email addresses that should be notified about this connection.</CardDescription>
              </div>
              <Dialog open={isAddEmailOpen} onOpenChange={setIsAddEmailOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Email
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Notification Email</DialogTitle>
                    <DialogDescription>
                      Add an email address to receive notifications for this connection.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddEmailOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddEmail} disabled={!newEmail}>
                      Add Email
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {emails.map((email, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{email}</span>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => handleRemoveEmail(email)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              {emails.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">No notification emails configured</div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Notification Settings</span>
            </CardTitle>
            <CardDescription>Choose what events should trigger notifications for this connection.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Connection Events</h4>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="connection-errors">Connection Errors</Label>
                    <p className="text-sm text-muted-foreground">
                      Get notified when the connection fails or encounters errors
                    </p>
                  </div>
                  <Switch
                    id="connection-errors"
                    checked={notifications.connectionErrors}
                    onCheckedChange={(checked) => handleNotificationChange("connectionErrors", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sync-failures">Sync Failures</Label>
                    <p className="text-sm text-muted-foreground">Get notified when data synchronization fails</p>
                  </div>
                  <Switch
                    id="sync-failures"
                    checked={notifications.syncFailures}
                    onCheckedChange={(checked) => handleNotificationChange("syncFailures", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="data-updates">Data Updates</Label>
                    <p className="text-sm text-muted-foreground">Get notified when new data is successfully synced</p>
                  </div>
                  <Switch
                    id="data-updates"
                    checked={notifications.dataUpdates}
                    onCheckedChange={(checked) => handleNotificationChange("dataUpdates", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="account-changes">Account Changes</Label>
                    <p className="text-sm text-muted-foreground">Get notified when the connected account changes</p>
                  </div>
                  <Switch
                    id="account-changes"
                    checked={notifications.accountChanges}
                    onCheckedChange={(checked) => handleNotificationChange("accountChanges", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="permission-changes">Permission Changes</Label>
                    <p className="text-sm text-muted-foreground">Get notified when account permissions are modified</p>
                  </div>
                  <Switch
                    id="permission-changes"
                    checked={notifications.permissionChanges}
                    onCheckedChange={(checked) => handleNotificationChange("permissionChanges", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="rate-limit-warnings">Rate Limit Warnings</Label>
                    <p className="text-sm text-muted-foreground">Get notified when approaching API rate limits</p>
                  </div>
                  <Switch
                    id="rate-limit-warnings"
                    checked={notifications.rateLimitWarnings}
                    onCheckedChange={(checked) => handleNotificationChange("rateLimitWarnings", checked)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
