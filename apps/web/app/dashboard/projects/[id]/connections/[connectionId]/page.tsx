"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation" // Import useRouter
import {
  Facebook,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  TestTube,
  Unlink,
  Plus,
  Copy,
  Trash2,
  Calendar,
  NetworkIcon as GenericConnectionIcon,
} from "lucide-react"
import { useProjectPage } from "@/lib/contexts/project-page-context"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"
import { MOCK_PROJECT_DATA } from "@/lib/mock-data" // Assuming MOCK_PROJECT_DATA is in mock-data.ts

// Mock data for a single connection
const connectionDetailsData = {
  id: "conn_001",
  name: "Company Facebook Page",
  platform: "Facebook",
  slug: "facebook", // Make sure this slug matches a key in PLATFORM_ICONS if used directly
  icon: Facebook,
  status: "active",
  lastSync: "2 hours ago",
  connectedAccount: { id: "fb_001", name: "@company_page", type: "Page", followers: "12.5K" },
  permissions: ["pages_read_engagement", "pages_show_list"],
  projectId: "proj_marketing_summer_24", // Added projectId for context
}

interface OAuthLink {
  id: string
  name: string
  url: string
  expiresAt: string
  createdAt: string
  status: "active" | "expired"
}

const initialOAuthLinks: OAuthLink[] = [
  {
    id: "oauth_link_001",
    name: "Dev Team Access",
    url: "https://api.example.com/oauth/conn_001/dev_token_xyz",
    expiresAt: "2025-12-31",
    createdAt: "2024-06-21",
    status: "active",
  },
  {
    id: "oauth_link_002",
    name: "Marketing Campaign Q3",
    url: "https://api.example.com/oauth/conn_001/mkt_token_abc",
    expiresAt: "2024-09-30",
    createdAt: "2024-06-15",
    status: "active",
  },
]

export default function SingleConnectionPage() {
  const params = useParams()
  const router = useRouter() // Initialize useRouter
  const currentProjectId = params.id as string // Renamed to avoid conflict with connection.projectId
  // const connectionId = params.connectionId as string // Use this to fetch real data

  const { setPageTitle, setPageIcon, setHeaderActions, setShowBackButton, setBackButtonHref, setBackButtonLabel } =
    useProjectPage()

  // For this example, we'll use the static mock data.
  // In a real app, you'd fetch this based on connectionId.
  const [connection, setConnection] = useState(connectionDetailsData)
  const connectionName = connection.name

  useEffect(() => {
    setPageTitle(`Manage: ${connectionName}`)
    setPageIcon(<GenericConnectionIcon className="h-5 w-5" />) // Using the renamed import
    setHeaderActions(null)
    setShowBackButton(true)
    setBackButtonHref(`/project/${currentProjectId}/connections`)
    setBackButtonLabel("Back to Connections")

    return () => {
      setShowBackButton(false)
      setBackButtonHref("")
      setBackButtonLabel("Back")
    }
  }, [
    setPageTitle,
    setPageIcon,
    setHeaderActions,
    setShowBackButton,
    setBackButtonHref,
    setBackButtonLabel,
    currentProjectId,
    connectionName,
  ])

  const [oauthLinks, setOAuthLinks] = useState<OAuthLink[]>(initialOAuthLinks)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [isCreateOAuthLinkOpen, setIsCreateOAuthLinkOpen] = useState(false)
  const [newOAuthLinkName, setNewOAuthLinkName] = useState("")
  const [newOAuthLinkExpiry, setNewOAuthLinkExpiry] = useState("")

  useEffect(() => {
    const today = new Date().toISOString().split("T")[0]
    setOAuthLinks((prevLinks) =>
      prevLinks.map((link) => ({
        ...link,
        status: link.expiresAt < today ? "expired" : "active",
      })),
    )
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "expired":
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = { active: "default", error: "destructive", expired: "secondary" } as const
    return <Badge variant={variants[status as keyof typeof variants] || "outline"}>{status}</Badge>
  }

  const handleTestConnection = async () => {
    setIsTestingConnection(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsTestingConnection(false)
    toast({
      title: "Connection tested",
      description: `${connection.platform} connection is working properly.`,
    })
  }

  const handleCreateOAuthLink = () => {
    if (!newOAuthLinkName || !newOAuthLinkExpiry) {
      toast({ title: "Missing fields", description: "Please provide a name and expiry date.", variant: "destructive" })
      return
    }
    const newLink: OAuthLink = {
      id: `oauth_link_${Math.random().toString(36).substring(2, 9)}`,
      name: newOAuthLinkName,
      url: `https://api.example.com/oauth/${connection.id}/${Math.random().toString(36).substring(2, 15)}`,
      createdAt: new Date().toISOString().split("T")[0],
      expiresAt: newOAuthLinkExpiry,
      status: new Date(newOAuthLinkExpiry) < new Date() ? "expired" : "active",
    }
    setOAuthLinks((prev) => [newLink, ...prev])
    toast({ title: "OAuth Link Created", description: `Link "${newLink.name}" generated.` })
    setIsCreateOAuthLinkOpen(false)
    setNewOAuthLinkName("")
    setNewOAuthLinkExpiry("")
  }

  const handleCopyLink = (url: string) => {
    navigator.clipboard.writeText(url)
    toast({ title: "Link Copied", description: "OAuth link copied to clipboard." })
  }

  const handleDeleteLink = (linkId: string) => {
    setOAuthLinks((prev) => prev.filter((link) => link.id !== linkId))
    toast({ title: "Link Deleted", description: "OAuth link has been deleted.", variant: "destructive" })
  }

  const handleReplaceAccount = () => {
    // 1. Simulate generating a unique link token for replacement
    const newLinkToken = `replacetoken_${connection.id}_${Date.now().toString(36)}`
    const projectCreatorEmail = MOCK_PROJECT_DATA[connection.projectId]?.creatorEmail || "<EMAIL>"

    // 2. In a real app, you would make an API call here to:
    //    - Create a new shareable link record in your database.
    //    - Associate it with the current project (currentProjectId),
    //      the platform (connection.slug),
    //      and mark it for "replacement" of this specific connection.
    //    - The API would return this newLinkToken.

    toast({
      title: "Initiating Account Replacement",
      description: "You'll be redirected to connect the new account.",
    })

    // 3. Redirect to the shareable link landing page
    const queryParams = new URLSearchParams({
      projectId: connection.projectId, // Use the projectId from the connection data
      platformSlug: connection.slug,
      purpose: "replaceAccount",
      creatorEmail: projectCreatorEmail, // Pass creator's email
      originalConnectionName: connection.name, // Pass current connection name for context
    })

    router.push(`/connect/link/${newLinkToken}?${queryParams.toString()}`)
  }

  const ConnectionPlatformIcon = connection.icon

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <ConnectionPlatformIcon className="h-8 w-8" />
        <h2 className="text-2xl font-semibold tracking-tight">Manage {connection.name}</h2>
        {getStatusBadge(connection.status)}
      </div>

      {/* Connection Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Connection Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm text-muted-foreground">Status</Label>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon(connection.status)}
                <span className="font-medium">{connection.status}</span>
              </div>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Last Sync</Label>
              <p className="font-medium mt-1">{connection.lastSync}</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleTestConnection}
              disabled={isTestingConnection || connection.status === "disconnected"}
            >
              {isTestingConnection ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <TestTube className="h-4 w-4 mr-2" />
              )}
              Test Connection
            </Button>
            {/* Add Reconnect button if status is error */}
          </div>
        </CardContent>
      </Card>

      {/* Connected Account */}
      {connection.connectedAccount && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Connected Account</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <ConnectionPlatformIcon className="h-5 w-5" />
                <div>
                  <div className="font-medium">{connection.connectedAccount.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {connection.connectedAccount.type} • {connection.connectedAccount.followers} followers
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="outline" onClick={handleReplaceAccount}>
                  Replace Account
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button size="sm" variant="outline" title="Disconnect Account">
                      <Unlink className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Disconnect Account</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will disconnect "{connection.connectedAccount.name}" from this connection. Feeds using this
                        connection will stop receiving data.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                        Disconnect
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generated OAuth Links */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Generated OAuth Links</CardTitle>
              <CardDescription>Manage OAuth 2.0 links for this connection.</CardDescription>
            </div>
            <Dialog open={isCreateOAuthLinkOpen} onOpenChange={setIsCreateOAuthLinkOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" /> Create Link
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New OAuth Link</DialogTitle>
                  <DialogDescription>Generate a new link with a name and expiry date.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="oauth-link-name">Link Name</Label>
                    <Input
                      id="oauth-link-name"
                      placeholder="e.g., Marketing Team Q3"
                      value={newOAuthLinkName}
                      onChange={(e) => setNewOAuthLinkName(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="oauth-link-expiry">Expiry Date</Label>
                    <Input
                      id="oauth-link-expiry"
                      type="date"
                      value={newOAuthLinkExpiry}
                      onChange={(e) => setNewOAuthLinkExpiry(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateOAuthLinkOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateOAuthLink}>Create Link</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {oauthLinks.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {oauthLinks.map((link) => (
                  <TableRow key={link.id}>
                    <TableCell>
                      <div className="font-medium">{link.name}</div>
                      <div className="text-xs text-muted-foreground font-mono truncate max-w-[200px]">{link.url}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(link.status)}
                        {getStatusBadge(link.status)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{link.expiresAt}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{link.createdAt}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          size="icon"
                          variant="outline"
                          onClick={() => handleCopyLink(link.url)}
                          title="Copy Link"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              size="icon"
                              variant="outline"
                              title="Delete Link"
                              disabled={link.status === "expired"}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete OAuth Link?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete the link "{link.name}"? This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteLink(link.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No OAuth links generated for this connection yet.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Permissions */}
      {connection.permissions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Permissions</CardTitle>
            <CardDescription>Current permissions granted for this platform.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {connection.permissions.map((permission) => (
                <Badge key={permission} variant="secondary">
                  {permission}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
