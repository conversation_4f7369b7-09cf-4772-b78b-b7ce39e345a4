"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Plus,
  Settings,
  Facebook,
  Instagram,
  Youtube,
  CheckCircle,
  XCircle,
  AlertCircle,
  Bell,
  Twitter,
  Linkedin,
  Twitch,
  Users,
  Link2,
  UserPlus,
  Copy,
  ArrowLeft,
  CheckCircle2,
  Loader2,
} from "lucide-react";
import type { LucideIcon } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON><PERSON>ger,
  DialogClose,
} from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

// Mock data for connections
const connectionsData = [
  {
    id: "conn_001",
    name: "Company Facebook Page",
    platform: "Facebook",
    slug: "facebook",
    icon: Facebook,
    status: "active",
    lastSync: "2 hours ago",
    connectedAccount: { name: "@company_page" },
  },
  {
    id: "conn_002",
    name: "Company Instagram",
    platform: "Instagram",
    slug: "instagram",
    icon: Instagram,
    status: "active",
    lastSync: "1 hour ago",
    connectedAccount: { name: "@company_insta" },
  },
  {
    id: "conn_003",
    name: "Company YouTube",
    platform: "YouTube",
    slug: "youtube",
    icon: Youtube,
    status: "error",
    lastSync: "3 days ago",
    connectedAccount: { name: "Company Channel" },
  },
];

interface PlatformTile {
  name: string;
  slug: string;
  icon: LucideIcon;
  color: string; // Tailwind color class for background/border
}

const AVAILABLE_PLATFORMS: PlatformTile[] = [
  {
    name: "Facebook",
    slug: "facebook",
    icon: Facebook,
    color: "bg-blue-600 hover:bg-blue-700",
  },
  {
    name: "Instagram",
    slug: "instagram",
    icon: Instagram,
    color: "bg-pink-500 hover:bg-pink-600",
  },
  {
    name: "Twitter",
    slug: "twitter",
    icon: Twitter,
    color: "bg-sky-500 hover:bg-sky-600",
  },
  {
    name: "YouTube",
    slug: "youtube",
    icon: Youtube,
    color: "bg-red-600 hover:bg-red-700",
  },
  {
    name: "LinkedIn",
    slug: "linkedin",
    icon: Linkedin,
    color: "bg-sky-700 hover:bg-sky-800",
  },
  {
    name: "Twitch",
    slug: "twitch",
    icon: Twitch,
    color: "bg-purple-600 hover:bg-purple-700",
  },
];

// Mock current user's email - in a real app, this would come from auth context
const MOCK_CURRENT_USER_EMAIL = "<EMAIL>";

export default function ConnectionsPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [isCreateConnectionOpen, setIsCreateConnectionOpen] = useState(false);
  const [newConnectionName, setNewConnectionName] = useState("");
  const [selectedPlatform, setSelectedPlatform] = useState<PlatformTile | null>(
    null
  );

  // New state for multi-step dialog
  const [currentStep, setCurrentStep] = useState(1); // 1: Choose method, 2: Configure
  const [connectionMethod, setConnectionMethod] = useState<
    "direct" | "link" | null
  >(null);
  const [generatedLink, setGeneratedLink] = useState<string | null>(null);
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const resetDialogState = () => {
    setNewConnectionName("");
    setSelectedPlatform(null);
    setCurrentStep(1);
    setConnectionMethod(null);
    setGeneratedLink(null);
    setIsGeneratingLink(false);
    setIsCopied(false);
  };

  const handleDialogClose = (isOpen: boolean) => {
    setIsCreateConnectionOpen(isOpen);
    if (!isOpen) {
      resetDialogState();
    }
  };

  const handleMethodSelect = (method: "direct" | "link") => {
    setConnectionMethod(method);
    setCurrentStep(2);
  };

  const handleCreateDirectConnection = () => {
    if (!newConnectionName || !selectedPlatform) {
      toast({
        title: "Missing Information",
        description: "Please provide a connection name and select a platform.",
        variant: "destructive",
      });
      return;
    }
    // Mock creation logic
    const newConnectionId = `conn_${Math.random().toString(36).substring(2, 7)}`;
    toast({
      title: "Connection created",
      description: `New ${selectedPlatform.name} connection "${newConnectionName}" has been initiated.`,
    });
    // In a real app, you'd refetch connections or update state
    // For now, let's navigate to the new connection's page (mock)
    router.push(`/project/${projectId}/connection/${newConnectionId}`);
    handleDialogClose(false); // Close dialog
  };

  const handleGenerateShareableLink = async () => {
    if (!newConnectionName || !selectedPlatform) {
      toast({
        title: "Missing Information",
        description: "Please provide a connection name and select a platform.",
        variant: "destructive",
      });
      return;
    }
    setIsGeneratingLink(true);
    setGeneratedLink(null); // Clear previous link

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const token = `linktoken_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const linkParams = new URLSearchParams({
      projectId: projectId,
      platformSlug: selectedPlatform.slug,
      connectionName: newConnectionName,
      creatorEmail: MOCK_CURRENT_USER_EMAIL, // Use mock email
      // Add any other necessary params for the link landing page
    });
    const fullLink = `${window.location.origin}/connect/link/${token}?${linkParams.toString()}`;

    setGeneratedLink(fullLink);
    setIsGeneratingLink(false);
    toast({
      title: "Link Generated",
      description: "Shareable link has been created.",
    });
  };

  const handleCopyToClipboard = () => {
    if (!generatedLink) return;
    navigator.clipboard
      .writeText(generatedLink)
      .then(() => {
        setIsCopied(true);
        toast({ title: "Copied to clipboard!" });
        setTimeout(() => setIsCopied(false), 2000);
      })
      .catch((err) => {
        toast({
          title: "Failed to copy",
          description: "Could not copy link to clipboard.",
          variant: "destructive",
        });
      });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = { active: "default", error: "destructive" } as const;
    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status}
      </Badge>
    );
  };

  const renderStep1 = () => (
    <>
      <DialogHeader>
        <DialogTitle>Create New Connection</DialogTitle>
        <DialogDescription>
          How would you like to connect this new social media account?
        </DialogDescription>
      </DialogHeader>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-6">
        <button
          onClick={() => handleMethodSelect("direct")}
          className="flex flex-col items-center justify-center p-6 rounded-lg border-2 border-border hover:border-primary hover:bg-muted transition-all text-center"
        >
          <UserPlus className="h-10 w-10 mb-3 text-primary" />
          <h3 className="text-lg font-semibold">Connect Myself</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Authorize the connection right now using your credentials.
          </p>
        </button>
        <button
          onClick={() => handleMethodSelect("link")}
          className="flex flex-col items-center justify-center p-6 rounded-lg border-2 border-border hover:border-primary hover:bg-muted transition-all text-center"
        >
          <Link2 className="h-10 w-10 mb-3 text-primary" />
          <h3 className="text-lg font-semibold">Generate Shareable Link</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Create a link for someone else to complete the connection.
          </p>
        </button>
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button variant="outline">Cancel</Button>
        </DialogClose>
      </DialogFooter>
    </>
  );

  const renderStep2 = () => (
    <>
      <DialogHeader>
        <div className="flex items-center mb-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setCurrentStep(1);
              setGeneratedLink(null);
            }}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-1" /> Back
          </Button>
          <DialogTitle>
            {connectionMethod === "direct"
              ? "Configure New Connection"
              : "Configure for Shareable Link"}
          </DialogTitle>
        </div>
        <DialogDescription>
          {connectionMethod === "direct"
            ? "Provide a name and select the platform for your new connection."
            : "Provide a name and select the platform. A link will be generated for this setup."}
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-6 py-4">
        <div className="grid gap-2">
          <Label htmlFor="connection-name">Connection Name</Label>
          <Input
            id="connection-name"
            placeholder="e.g., Company Instagram Account"
            value={newConnectionName}
            onChange={(e) => setNewConnectionName(e.target.value)}
            disabled={isGeneratingLink || !!generatedLink}
          />
        </div>
        <div className="grid gap-2">
          <Label>Select Platform</Label>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {AVAILABLE_PLATFORMS.map((platform) => {
              const IconComponent = platform.icon;
              return (
                <button
                  key={platform.slug}
                  type="button"
                  onClick={() => setSelectedPlatform(platform)}
                  disabled={isGeneratingLink || !!generatedLink}
                  className={cn(
                    "flex flex-col items-center justify-center p-4 rounded-lg border-2 transition-all duration-150 ease-in-out",
                    "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                    selectedPlatform?.slug === platform.slug
                      ? "border-primary ring-2 ring-primary shadow-lg"
                      : "border-border hover:border-primary/70 hover:shadow-md",
                    (isGeneratingLink || !!generatedLink) &&
                      "opacity-70 cursor-not-allowed"
                  )}
                >
                  <IconComponent
                    className={cn(
                      "h-8 w-8 mb-2",
                      selectedPlatform?.slug === platform.slug
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  />
                  <span
                    className={cn(
                      "text-sm font-medium",
                      selectedPlatform?.slug === platform.slug
                        ? "text-primary"
                        : "text-foreground"
                    )}
                  >
                    {platform.name}
                  </span>
                </button>
              );
            })}
          </div>
          {!selectedPlatform && connectionMethod === "direct" && (
            <p className="text-xs text-red-500">Please select a platform.</p>
          )}
        </div>

        {connectionMethod === "link" && generatedLink && (
          <div className="space-y-2 pt-4 border-t">
            <Label className="font-semibold">Shareable Link:</Label>
            <div className="flex items-center space-x-2">
              <Input
                type="text"
                readOnly
                value={generatedLink}
                className="bg-muted"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={handleCopyToClipboard}
                disabled={isCopied}
              >
                {isCopied ? (
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              This link is valid for 48 hours (mock duration).
            </p>
          </div>
        )}
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button
            variant="outline"
            onClick={() => {
              if (generatedLink)
                resetDialogState(); /* else do nothing, let DialogClose handle */
            }}
          >
            {generatedLink ? "Done" : "Cancel"}
          </Button>
        </DialogClose>
        {connectionMethod === "direct" && (
          <Button
            onClick={handleCreateDirectConnection}
            disabled={!newConnectionName || !selectedPlatform}
          >
            Create & Connect
          </Button>
        )}
        {connectionMethod === "link" && !generatedLink && (
          <Button
            onClick={handleGenerateShareableLink}
            disabled={
              !newConnectionName || !selectedPlatform || isGeneratingLink
            }
          >
            {isGeneratingLink && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Generate Link
          </Button>
        )}
      </DialogFooter>
    </>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Social Media Connections</CardTitle>
              <CardDescription>
                Manage your social media platform connections. Each connection
                can generate OAuth2.0 links.
              </CardDescription>
            </div>
            <Dialog
              open={isCreateConnectionOpen}
              onOpenChange={handleDialogClose}
            >
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Connection
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-lg">
                {currentStep === 1 ? renderStep1() : renderStep2()}
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Connection</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Sync</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {connectionsData.map((connection) => {
                const IconComponent = connection.icon;
                return (
                  <TableRow key={connection.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <IconComponent className="h-5 w-5" />
                        <div>
                          <div className="font-medium">{connection.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {connection.connectedAccount?.name ||
                              "Not connected"}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{connection.platform}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(connection.status)}
                        {getStatusBadge(connection.status)}
                      </div>
                    </TableCell>
                    <TableCell>{connection.lastSync}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Link
                          href={`/project/${projectId}/connection/${connection.id}`}
                        >
                          <Button size="sm" variant="outline">
                            <Settings className="h-4 w-4 mr-1" />
                            Manage
                          </Button>
                        </Link>
                        <Link
                          href={`/project/${projectId}/connection/${connection.id}/notifications`}
                        >
                          <Button
                            size="sm"
                            variant="outline"
                            title="Notification Settings"
                          >
                            <Bell className="h-4 w-4" />
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
