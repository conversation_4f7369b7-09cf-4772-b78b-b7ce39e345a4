"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { Save, SettingsIcon, Filter, SortAsc, Facebook, Instagram, Youtube, Rss } from "lucide-react"
import type { LucideIcon } from "lucide-react"
import Link from "next/link"

import { useProjectPage } from "@/lib/contexts/project-page-context"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"

// Mock data (as in your previous version)
interface FeedItem {
  id: string
  platform: "Facebook" | "Instagram" | "YouTube"
  platformIcon: LucideIcon
  userName: string
  userAvatar: string
  content: string
  mediaUrl?: string
  mediaType?: "image" | "video"
  timestamp: string
  likes: number
  comments: number
  views?: number
}
const mockFeedPreviewItems: FeedItem[] = [
  {
    id: "fp_001",
    platform: "Instagram",
    platformIcon: Instagram,
    userName: "@insta_user123",
    userAvatar: "/placeholder.svg?width=40&height=40",
    content:
      "Loving the new features! #awesome #tech This is a longer piece of content to see how it wraps and displays within the card. We should ensure that text truncation or wrapping is handled gracefully for user experience.",
    mediaUrl: "/placeholder.svg?width=600&height=400",
    mediaType: "image",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    likes: 152,
    comments: 12,
  },
  {
    id: "fp_002",
    platform: "Facebook",
    platformIcon: Facebook,
    userName: "Facebook Page Official",
    userAvatar: "/placeholder.svg?width=40&height=40",
    content:
      "Check out our latest blog post on the future of AI. Link in bio! We've published an in-depth analysis that you won't want to miss. It covers all the recent breakthroughs and potential societal impacts.",
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
    likes: 340,
    comments: 45,
  },
  {
    id: "fp_003",
    platform: "YouTube",
    platformIcon: Youtube,
    userName: "Tech Reviews YT",
    userAvatar: "/placeholder.svg?width=40&height=40",
    content:
      "New Video: Unboxing the latest gadget! You won't believe what's inside. This is our most exciting unboxing yet, featuring the XG-5000.",
    mediaUrl: "/placeholder.svg?width=600&height=338",
    mediaType: "video",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    likes: 1200,
    comments: 150,
    views: 25000,
  },
]

const feedData = {
  id: "feed_001",
  name: "Main Social Feed",
  description: "Aggregated feed from all social platforms",
  status: "active",
  connections: ["conn_001", "conn_002"],
  sortBy: "date",
  filters: ["verified_only"],
  postsCount: 1240,
}
const availableConnections = [
  { id: "conn_001", name: "Company Facebook Page", platform: "Facebook", icon: Facebook },
  { id: "conn_002", name: "Company Instagram", platform: "Instagram", icon: Instagram },
  { id: "conn_003", name: "Company YouTube", platform: "YouTube", icon: Youtube },
]
const sortOptions = [
  { value: "date", label: "Date (Newest First)" },
  { value: "date_asc", label: "Date (Oldest First)" },
  { value: "engagement", label: "Engagement Rate" },
  { value: "views", label: "View Count" },
  { value: "likes", label: "Like Count" },
  { value: "comments", label: "Comment Count" },
]
const filterOptions = [
  { value: "verified_only", label: "Verified accounts only" },
  { value: "video_only", label: "Video content only" },
  { value: "image_only", label: "Image content only" },
  { value: "stories_only", label: "Stories only" },
  { value: "min_engagement", label: "Minimum engagement threshold" },
  { value: "hashtag_filter", label: "Specific hashtags" },
]

export default function FeedConfigurationPage() {
  const params = useParams()
  const projectId = params.id as string
  const feedId = params.feedId as string

  const { setPageTitle, setPageIcon, setHeaderActions, setShowBackButton, setBackButtonHref, setBackButtonLabel } =
    useProjectPage()

  const [feedName, setFeedName] = useState(feedData.name)
  const [feedDescription, setFeedDescription] = useState(feedData.description)
  const [selectedConnections, setSelectedConnections] = useState(feedData.connections)
  const [sortBy, setSortBy] = useState(feedData.sortBy)
  const [activeFilters, setActiveFilters] = useState(feedData.filters)
  const [isActive, setIsActive] = useState(feedData.status === "active")
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [previewItems, setPreviewItems] = useState<FeedItem[]>(mockFeedPreviewItems)

  const handleSaveSettings = () => {
    setHasUnsavedChanges(false)
    toast({
      title: "Feed updated",
      description: "Feed configuration has been saved successfully.",
    })
  }

  useEffect(() => {
    setPageTitle(`Configure: ${feedName}`) // Use dynamic feedName
    setPageIcon(<Rss className="h-5 w-5" />)
    setHeaderActions(
      <>
        <Link href={`/project/${projectId}/feed/${feedId}/api-settings`}>
          <Button variant="outline" size="sm">
            <SettingsIcon className="h-4 w-4 mr-2" />
            API Settings
          </Button>
        </Link>
        <Button onClick={handleSaveSettings} disabled={!hasUnsavedChanges} size="sm">
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </Button>
        {hasUnsavedChanges && <Badge variant="secondary">Unsaved</Badge>}
      </>,
    )
    setShowBackButton(true)
    setBackButtonHref(`/project/${projectId}/feeds`)
    setBackButtonLabel("Back to Feeds")

    return () => {
      setShowBackButton(false)
    }
  }, [
    setPageTitle,
    setPageIcon,
    setHeaderActions,
    setShowBackButton,
    setBackButtonHref,
    setBackButtonLabel,
    projectId,
    feedId,
    feedName, // Add feedName to dependencies
    hasUnsavedChanges, // Add hasUnsavedChanges for button state
  ])

  const handleConnectionToggle = (connectionId: string) => {
    setSelectedConnections((prev) => {
      const newConnections = prev.includes(connectionId)
        ? prev.filter((id) => id !== connectionId)
        : [...prev, connectionId]
      setHasUnsavedChanges(true)
      return newConnections
    })
  }

  const handleFilterToggle = (filterValue: string) => {
    setActiveFilters((prev) => {
      const newFilters = prev.includes(filterValue) ? prev.filter((f) => f !== filterValue) : [...prev, filterValue]
      setHasUnsavedChanges(true)
      return newFilters
    })
  }

  const handleInputChange = (setter: (value: string) => void) => (value: string) => {
    setter(value)
    setHasUnsavedChanges(true)
  }

  const handleSortChange = (value: string) => {
    setSortBy(value)
    setHasUnsavedChanges(true)
  }

  const handleStatusToggle = (checked: boolean) => {
    setIsActive(checked)
    setHasUnsavedChanges(true)
  }

  useEffect(() => {
    let items = [...mockFeedPreviewItems]
    if (activeFilters.includes("video_only")) {
      items = items.filter((item) => item.mediaType === "video")
    }
    if (activeFilters.includes("image_only")) {
      items = items.filter((item) => item.mediaType === "image")
    }

    if (sortBy === "likes") {
      items.sort((a, b) => b.likes - a.likes)
    } else if (sortBy === "comments") {
      items.sort((a, b) => b.comments - a.comments)
    } else if (sortBy === "views" && items.some((item) => item.views !== undefined)) {
      items.sort((a, b) => (b.views || 0) - (a.views || 0))
    } else {
      // Default to date
      items.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    }

    setPreviewItems(items.slice(0, 3)) // Show top 3 for preview
  }, [sortBy, activeFilters, selectedConnections])

  return (
    <div className="space-y-6">
      {" "}
      {/* Max-width can be applied here or in the layout if needed */}
      {/* Basic Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Settings</CardTitle>
          <CardDescription>Configure the basic properties of your feed.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="feed-name">Feed Name</Label>
            <Input
              id="feed-name"
              value={feedName}
              onChange={(e) => handleInputChange(setFeedName)(e.target.value)}
              placeholder="Enter feed name"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="feed-description">Description</Label>
            <Textarea
              id="feed-description"
              value={feedDescription}
              onChange={(e) => handleInputChange(setFeedDescription)(e.target.value)}
              placeholder="Enter feed description"
              rows={3}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="feed-status">Feed Status</Label>
              <p className="text-sm text-muted-foreground">Enable or disable this feed</p>
            </div>
            <Switch id="feed-status" checked={isActive} onCheckedChange={handleStatusToggle} />
          </div>
        </CardContent>
      </Card>
      {/* Connection Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Data Sources</CardTitle>
          <CardDescription>Select which connections this feed should aggregate data from.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {availableConnections.map((connection) => {
              const IconComponent = connection.icon
              const isSelected = selectedConnections.includes(connection.id)
              return (
                <div key={connection.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <IconComponent className="h-8 w-8" />
                    <div>
                      <h3 className="font-medium">{connection.name}</h3>
                      <p className="text-sm text-muted-foreground">{connection.platform}</p>
                    </div>
                  </div>
                  <Switch checked={isSelected} onCheckedChange={() => handleConnectionToggle(connection.id)} />
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {/* Sorting Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <SortAsc className="h-5 w-5" />
            <span>Sorting</span>
          </CardTitle>
          <CardDescription>Configure how posts should be sorted in this feed.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2">
            <Label htmlFor="sort-by">Sort By</Label>
            <select
              id="sort-by"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>
      {/* Filtering Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
          <CardDescription>Apply filters to control what content appears in this feed.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filterOptions.map((filter) => {
              const isActiveFilter = activeFilters.includes(filter.value)
              return (
                <div key={filter.value} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <Label className="font-medium">{filter.label}</Label>
                  </div>
                  <Switch checked={isActiveFilter} onCheckedChange={() => handleFilterToggle(filter.value)} />
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {/* Feed Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Feed Preview</CardTitle>
          <CardDescription>A sample of what your configured feed will look like.</CardDescription>
        </CardHeader>
        <CardContent>
          {previewItems.length > 0 ? (
            <div className="space-y-4">
              {previewItems.map((item) => {
                const ItemIcon = item.platformIcon
                return (
                  <Card key={item.id} className="overflow-hidden">
                    <CardHeader className="flex flex-row items-start bg-muted/50 p-4">
                      <img
                        src={item.userAvatar || "/placeholder.svg"}
                        alt={item.userName}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                      <div className="grid gap-0.5">
                        <CardTitle className="group flex items-center gap-2 text-lg">
                          {item.userName}
                          <ItemIcon className="h-4 w-4 text-muted-foreground" />
                        </CardTitle>
                        <CardDescription>{new Date(item.timestamp).toLocaleString()}</CardDescription>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 text-sm">
                      <p className="mb-3 whitespace-pre-wrap">{item.content}</p>
                      {item.mediaUrl && item.mediaType === "image" && (
                        <img
                          src={item.mediaUrl || "/placeholder.svg"}
                          alt="Feed media"
                          className="rounded-md border max-h-96 object-cover w-full"
                        />
                      )}
                      {item.mediaUrl && item.mediaType === "video" && (
                        <div className="relative aspect-video">
                          <img
                            src={item.mediaUrl || "/placeholder.svg"}
                            alt="Feed video thumbnail"
                            className="rounded-md border object-cover w-full h-full"
                          />
                          <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                            <Youtube className="h-12 w-12 text-white/80" /> {/* Or a generic play icon */}
                          </div>
                        </div>
                      )}
                    </CardContent>
                    <div className="flex items-center p-4 pt-0 bg-muted/50 border-t">
                      <div className="flex space-x-4 text-sm text-muted-foreground">
                        <div>
                          Likes: <span className="font-semibold text-foreground">{item.likes}</span>
                        </div>
                        <div>
                          Comments: <span className="font-semibold text-foreground">{item.comments}</span>
                        </div>
                        {item.views !== undefined && (
                          <div>
                            Views: <span className="font-semibold text-foreground">{item.views.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                )
              })}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No items match your current filter and connection settings.
            </p>
          )}
        </CardContent>
      </Card>
      {/* Feed Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Feed Statistics</CardTitle>
          <CardDescription>Current statistics for this feed.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{feedData.postsCount.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Total Posts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{selectedConnections.length}</div>
              <div className="text-sm text-muted-foreground">Connected Sources</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{activeFilters.length}</div>
              <div className="text-sm text-muted-foreground">Active Filters</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{isActive ? "Active" : "Paused"}</div>
              <div className="text-sm text-muted-foreground">Status</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
