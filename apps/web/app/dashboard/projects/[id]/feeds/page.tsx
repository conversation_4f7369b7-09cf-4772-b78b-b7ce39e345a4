"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import {
  Plus,
  Settings,
  Key,
  CheckCircle,
  XCircle,
  AlertCircle,
  Rss,
  Facebook,
  Instagram,
  Youtube,
  Twitter,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/hooks/use-toast"
import { useProjectPage } from "@/lib/contexts/project-page-context" // Assuming this context exists

// Mock data for connections (should be fetched for the project)
const MOCK_PROJECT_CONNECTIONS = [
  { id: "conn_001", name: "Company Facebook Page", platform: "Facebook", icon: Facebook },
  { id: "conn_002", name: "Company Instagram", platform: "Instagram", icon: Instagram },
  { id: "conn_003", name: "Company YouTube", platform: "YouTube", icon: Youtube },
  { id: "conn_004", name: "Personal Twitter", platform: "Twitter", icon: Twitter },
]

// Mock data for feeds
const feedsData = [
  {
    id: "feed_001",
    name: "Main Social Feed",
    description: "Aggregated feed from all social platforms",
    connectionsCount: 2,
    status: "active",
    lastUpdated: "5 minutes ago",
    postsCount: 1240,
  },
  {
    id: "feed_002",
    name: "Instagram Stories Feed",
    description: "Instagram stories and highlights only",
    connectionsCount: 1,
    status: "active",
    lastUpdated: "1 hour ago",
    postsCount: 89,
  },
  {
    id: "feed_003",
    name: "Video Content Feed",
    description: "Video content from YouTube and Instagram",
    connectionsCount: 2,
    status: "paused",
    lastUpdated: "2 days ago",
    postsCount: 156,
  },
]

export default function FeedsPage() {
  const params = useParams()
  const router = useRouter()
  const projectId = params.id as string
  const { setPageTitle, setPageIcon, setHeaderActions } = useProjectPage()

  useEffect(() => {
    setPageTitle("Data Feeds")
    setPageIcon(<Rss className="h-5 w-5" />)
    setHeaderActions(null)
  }, [setPageTitle, setPageIcon, setHeaderActions])

  const [isCreateFeedOpen, setIsCreateFeedOpen] = useState(false)
  const [newFeedName, setNewFeedName] = useState("")
  const [newFeedDescription, setNewFeedDescription] = useState("")
  const [selectedConnections, setSelectedConnections] = useState<string[]>([])

  const handleCreateFeed = () => {
    if (!newFeedName || selectedConnections.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please provide a feed name and select at least one connection.",
        variant: "destructive",
      })
      return
    }
    // Mock creation logic
    const newFeedId = `feed_${Math.random().toString(36).substring(2, 7)}`
    toast({
      title: "Feed created",
      description: `New feed "${newFeedName}" has been created with ${selectedConnections.length} connection(s).`,
    })
    setIsCreateFeedOpen(false)
    setNewFeedName("")
    setNewFeedDescription("")
    setSelectedConnections([])
    router.push(`/project/${projectId}/feed/${newFeedId}`)
  }

  const handleConnectionToggle = (connectionId: string) => {
    setSelectedConnections((prev) =>
      prev.includes(connectionId) ? prev.filter((id) => id !== connectionId) : [...prev, connectionId],
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "paused":
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = { active: "default", paused: "secondary" } as const
    return <Badge variant={variants[status as keyof typeof variants] || "outline"}>{status}</Badge>
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Data Feeds</CardTitle>
              <CardDescription>
                Manage feeds that aggregate data from your connections. Each feed has its own API settings.
              </CardDescription>
            </div>
            <Dialog open={isCreateFeedOpen} onOpenChange={setIsCreateFeedOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Feed
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[525px]">
                <DialogHeader>
                  <DialogTitle>Create New Feed</DialogTitle>
                  <DialogDescription>Create a new feed to aggregate data from your connections.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="feed-name">Feed Name</Label>
                    <Input
                      id="feed-name"
                      placeholder="e.g., Main Social Feed"
                      value={newFeedName}
                      onChange={(e) => setNewFeedName(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="feed-description">Description (Optional)</Label>
                    <Input
                      id="feed-description"
                      placeholder="e.g., Aggregated feed from all social platforms"
                      value={newFeedDescription}
                      onChange={(e) => setNewFeedDescription(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label>Select Connections</Label>
                    <Card className="max-h-60 overflow-y-auto">
                      <CardContent className="p-4 space-y-3">
                        {MOCK_PROJECT_CONNECTIONS.length > 0 ? (
                          MOCK_PROJECT_CONNECTIONS.map((conn) => {
                            const IconComponent = conn.icon
                            return (
                              <div
                                key={conn.id}
                                className="flex items-center space-x-3 p-2 rounded-md hover:bg-muted/50"
                              >
                                <Checkbox
                                  id={`conn-${conn.id}`}
                                  checked={selectedConnections.includes(conn.id)}
                                  onCheckedChange={() => handleConnectionToggle(conn.id)}
                                />
                                <IconComponent className="h-5 w-5 text-muted-foreground" />
                                <Label htmlFor={`conn-${conn.id}`} className="flex-1 cursor-pointer">
                                  {conn.name} <span className="text-xs text-muted-foreground">({conn.platform})</span>
                                </Label>
                              </div>
                            )
                          })
                        ) : (
                          <p className="text-sm text-muted-foreground text-center py-4">
                            No connections available. Please create a connection first.
                          </p>
                        )}
                      </CardContent>
                    </Card>
                    {selectedConnections.length === 0 && (
                      <p className="text-xs text-red-500">Please select at least one connection.</p>
                    )}
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateFeedOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateFeed} disabled={!newFeedName || selectedConnections.length === 0}>
                    Create Feed
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Feed</TableHead>
                <TableHead>Connections</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Posts</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {feedsData.map((feed) => (
                <TableRow key={feed.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Rss className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{feed.name}</div>
                        <div className="text-sm text-muted-foreground">{feed.description}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{feed.connectionsCount}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(feed.status)}
                      {getStatusBadge(feed.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{feed.postsCount.toLocaleString()}</div>
                  </TableCell>
                  <TableCell>{feed.lastUpdated}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Link href={`/project/${projectId}/feed/${feed.id}`}>
                        <Button size="sm" variant="outline">
                          <Settings className="h-4 w-4 mr-1" />
                          Configure
                        </Button>
                      </Link>
                      <Link href={`/project/${projectId}/feed/${feed.id}/api-settings`}>
                        <Button size="sm" variant="outline" title="API Settings">
                          <Key className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
