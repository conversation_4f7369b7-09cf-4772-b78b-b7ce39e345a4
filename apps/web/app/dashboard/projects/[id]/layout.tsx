import type React from "react";

import { ProjectHeaderContent } from "@/components/dashboard/ProjectHeaderContent";
import { ProjectHorizontalNav } from "@/components/dashboard/ProjectHorizontalNav";
import { getProject } from "@/lib/fetch/getProject";

export default async function ProjectDetailLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const data = await getProject({ projectId: id });

  const { name, id: projectId } = data;

  return (
    <div className="flex flex-col h-full pt-4">
      <ProjectHeaderContent name={name} projectId={projectId} />
      <ProjectHorizontalNav />
      <main className="flex-1 p-4 md:p-6 overflow-y-auto bg-muted/30">
        {children}
      </main>
    </div>
  );
}
