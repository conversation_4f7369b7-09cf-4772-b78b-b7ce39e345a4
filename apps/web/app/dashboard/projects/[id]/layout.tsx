import type React from "react";

import { ProjectPageProvider } from "@/lib/contexts/project-page-context"; // Ensure this path is correct
import { ProjectHeaderContent } from "@/components/dashboard/ProjectHeaderContent";
import { ProjectHorizontalNav } from "@/components/dashboard/ProjectHorizontalNav";

export default function ProjectDetailLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProjectPageProvider>
      <div className="flex flex-col h-full">
        <ProjectHeaderContent />
        <ProjectHorizontalNav />
        <main className="flex-1 p-4 md:p-6 overflow-y-auto bg-muted/30">
          {children}
        </main>
      </div>
    </ProjectPageProvider>
  );
}
