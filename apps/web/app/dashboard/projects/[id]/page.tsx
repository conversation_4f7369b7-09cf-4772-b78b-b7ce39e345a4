import ProjectHeaderInformationSetter from "@/components/dashboard/ProjectHeaderInformationSetter";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { createApiClient } from "@/lib/api";
import { getProject } from "@/lib/fetch/getProject";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { LayoutDashboard } from "lucide-react";
import Link from "next/link";

// Mock data for demonstration
const MOCK_PROJECT_OVERVIEW_DATA = {
  totalConnections: 5,
  activeFeeds: 3,
  recentActivity: [
    "Feed 'Product Updates' synced successfully.",
    "New connection 'Twitter - Company X' added.",
    "API Key for 'Main Feed' accessed.",
  ],
};

export default async function ProjectOverviewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const projectId = (await params).id;

  // Fetch actual overview data here based on projectId
  const overviewData = MOCK_PROJECT_OVERVIEW_DATA;

  const data = await getProject({ projectId });

  console.log("PROJECT DATA", data);

  return (
    <div className="space-y-6">
      <ProjectHeaderInformationSetter
        pageTitle="Project Overview"
        projectTitle="Project Name"
        pageIcon={<LayoutDashboard className="h-5 w-5" />}
        showBackButton={false}
      />
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Total Connections</CardTitle>
            <CardDescription>
              Number of active social media connections.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold">
              {overviewData.totalConnections}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Active Feeds</CardTitle>
            <CardDescription>Number of configured data feeds.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold">{overviewData.activeFeeds}</p>
          </CardContent>
        </Card>
        <Card className="md:col-span-2 lg:col-span-1">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks for this project.</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col space-y-2">
            <Link href={`/project/${projectId}/connections`} passHref>
              <Button variant="outline" className="w-full justify-start">
                Manage Connections
              </Button>
            </Link>
            <Link href={`/project/${projectId}/feeds`} passHref>
              <Button variant="outline" className="w-full justify-start">
                Manage Feeds
              </Button>
            </Link>
            <Link href={`/project/${projectId}/settings`} passHref>
              <Button variant="outline" className="w-full justify-start">
                Project Settings
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Latest events and actions within this project.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {overviewData.recentActivity.length > 0 ? (
            <ul className="space-y-2 text-sm text-muted-foreground">
              {overviewData.recentActivity.map((activity, index) => (
                <li key={index} className="border-l-2 pl-3 border-primary">
                  {activity}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-muted-foreground">No recent activity.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
