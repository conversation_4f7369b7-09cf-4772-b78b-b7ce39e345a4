"use client";

import type React from "react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { ArrowLeft, ImageIcon as DefaultIcon } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useProjectPage } from "@/lib/contexts/project-page-context"; // Ensure this path is correct

// This component renders the top bar
export function ProjectHeaderContent() {
  const params = useParams();
  const projectId = params.id as string;
  const {
    pageTitle,
    projectTitle,
    pageIcon,
    showBackButton,
    backButtonHref,
    backButtonLabel,
  } = useProjectPage();

  return (
    <div className="border-b bg-background">
      <div className="flex h-16 items-center px-4 md:px-6">
        <div className="flex flex-1 items-center space-x-2 md:space-x-4">
          {showBackButton && backButtonHref && (
            <>
              <Link href={backButtonHref}>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <ArrowLeft className="h-5 w-5" />
                  <span className="sr-only">Back</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hidden md:inline-flex text-sm"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  {backButtonLabel}
                </Button>
              </Link>
              <Separator
                orientation="vertical"
                className="h-6 hidden md:block"
              />
            </>
          )}
          <div className="flex items-center space-x-2 md:space-x-3">
            {pageIcon || (
              <DefaultIcon className="h-5 w-5 text-muted-foreground" />
            )}{" "}
            {/* Default icon if none provided */}
            <h1
              className="text-base md:text-lg font-semibold truncate"
              title={pageTitle || "Page"}
            >
              {pageTitle || "Page"} {/* Fallback page title */}
            </h1>
            <Badge variant="outline" className="text-xs whitespace-nowrap">
              Project: {projectTitle ?? "Loading..."} (ID: {projectId})
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
}
