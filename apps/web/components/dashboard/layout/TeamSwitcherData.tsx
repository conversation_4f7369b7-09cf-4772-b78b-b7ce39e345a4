import React from "react";
import { TeamSwitcher } from "./team-switcher";
import { getUser } from "@propelauth/nextjs/server/app-router";

const TeamSwitcherData = async () => {
  const user = await getUser();
  const orgs = user?.getOrgs();
  const activeOrgId = user?.getActiveOrgId();
  const teams =
    orgs
      ?.sort((a, b) => {
        if (a.orgId === activeOrgId) {
          return -1;
        }
        if (b.orgId === activeOrgId) {
          return 1;
        }
        return 0;
      })
      .map((org) => {
        const orgInitials = org.orgName
          .split(" ")
          .map((word) => word[0])
          .join("")
          .substring(0, 2)
          .toUpperCase();
        const orgLogo =
          orgInitials.length === 2
            ? orgInitials
            : org.orgName.substring(0, 2).toUpperCase();
        return {
          name: org.orgName,
          logoUrl: org.orgMetadata?.logoUrl,
          plan: "Standard",
        };
      }) ?? [];

  console.log("ORGS", orgs);
  return <TeamSwitcher teams={teams} />;
};

export default TeamSwitcherData;
