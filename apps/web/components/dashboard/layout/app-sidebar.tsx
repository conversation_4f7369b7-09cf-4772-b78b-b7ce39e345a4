import type * as React from "react";

import TeamSwitcherData from "./TeamSwitcherData";
import { TooltipProvider } from "@/components/ui/tooltip";
import { getUser } from "@propelauth/nextjs/server/app-router";
import NavUserData from "./NavUserData";
import SidebarNavMenu from "./nav-menu";

export const AppSidebar: React.FC = async ({ ...props }) => {
  const user = await getUser();
  return (
    <div className="shrink-0 w-[100px] py-7 px-4 flex flex-col rounded-3xl justify-between bg-sidebar">
      <div className="flex flex-col gap-7 justify-center">
        <TeamSwitcherData />
        <TooltipProvider>
          <SidebarNavMenu />
        </TooltipProvider>
      </div>
      {user && <NavUserData />}
    </div>
  );
};

export const SidebarInset: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="max-h-[calc(100dvh-3.5rem)] bg-background rounded-3xl w-full">
      {children}
    </div>
  );
};

export const SidebarWrapper: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="h-dvh w-full p-7 bg-foreground fixed flex flex-row gap-7">
      {children}
    </div>
  );
};
