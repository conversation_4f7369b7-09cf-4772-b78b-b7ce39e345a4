"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";

export const getProject = async ({ projectId }: { projectId: string }) => {
  try {
    const accessToken = await getAccessTokenAsync();
    if (!accessToken) throw new Error("No access token");
    const user = await getUser();
    if (!user) throw new Error("No user");
    const orgId = user?.getActiveOrg()?.orgId;
    if (!orgId) throw new Error("No org id");
    const apiClient = createApiClient(accessToken, orgId);
    const res = await apiClient.manage.projects[":projectId"].$get(
      {
        param: {
          projectId,
        },
      },
      {
        init: {
          next: {
            revalidate: 60, // Daten alle 60 Sekunden revalidieren
            tags: ["project", projectId], // Cache-Tag für gezielte Revalidierung
          },
        },
      }
    );
    const projects = await res.json();
    return projects;
  } catch (e) {
    console.error("Error fetching project:", e);
    return null;
  }
};
