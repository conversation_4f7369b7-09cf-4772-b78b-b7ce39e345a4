import { customAlphabet } from "nanoid";

export const shortId = customAlphabet(
  "2346789abcdefghijkmnpqrtwxyzABCDEFGHJKLMNPQRTUVWXYZ",
  10
);

export const middleId = customAlphabet(
  "2346789abcdefghijkmnpqrtwxyzABCDEFGHJKLMNPQRTUVWXYZ",
  16
);

export const longId = customAlphabet(
  "2346789abcdefghijkmnpqrtwxyzABCDEFGHJKLMNPQRTUVWXYZ",
  21
);

export const dbId = () =>
  `${customAlphabet(
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",
    5
  )()}-${customAlphabet("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", 5)()}-${customAlphabet(
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",
    5
  )()}`;
